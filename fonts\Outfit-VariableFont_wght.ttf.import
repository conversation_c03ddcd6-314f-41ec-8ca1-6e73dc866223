[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://6ir14f6f478t"
path="res://.godot/imported/Outfit-VariableFont_wght.ttf-483b99129fa522520f0ad3daa3ecc14c.fontdata"

[deps]

source_file="res://fonts/Outfit-VariableFont_wght.ttf"
dest_files=["res://.godot/imported/Outfit-VariableFont_wght.ttf-483b99129fa522520f0ad3daa3ecc14c.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
