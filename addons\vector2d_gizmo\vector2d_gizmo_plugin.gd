@tool
extends EditorPlugin

# Whether gizmos are visible in the 2D editor
var show_gizmos: bool = true
# Info for current drag: { node: Node2D, prop: String }
var dragging_info = null
# Reference to the menu button for cleanup
var menu_button: MenuButton = null

func _enter_tree() -> void:
	print("Vector2D Gizmo Plugin: Entering tree")
	# Add "Vector2D Gizmos" submenu under View → View Options
	menu_button = MenuButton.new()
	menu_button.text = "Vector2D Gizmos"
	var popup = menu_button.get_popup()
	popup.add_item("Show Gizmos", 0)
	popup.add_item("Hide Gizmos", 1)
	popup.connect("id_pressed", Callable(self, "_on_view_option_selected"))
	add_control_to_container(CONTAINER_CANVAS_EDITOR_MENU, menu_button)

	# Enable force draw over forwarding to ensure our overlay function gets called
	set_force_draw_over_forwarding_enabled()

func _exit_tree() -> void:
	print("Vector2D Gizmo Plugin: Exiting tree")
	# Properly cleanup the menu button
	if menu_button != null:
		remove_control_from_container(CONTAINER_CANVAS_EDITOR_MENU, menu_button)
		menu_button.queue_free()
		menu_button = null
	# Clear any dragging state
	dragging_info = null

func _on_view_option_selected(id: int) -> void:
	show_gizmos = (id == 0)
	print("Vector2D Gizmo: Show gizmos set to: ", show_gizmos)
	update_viewport()

func update_viewport() -> void:
	# Redraw the 2D editor viewport overlays
	update_overlays()

# Draw gizmos each frame in the 2D editor
func _forward_canvas_draw_over_viewport(overlay):
	if not show_gizmos:
		return

	# Get the selected nodes from the editor selection
	var selection = get_editor_interface().get_selection()
	var selected_nodes = selection.get_selected_nodes()

	print("Vector2D Gizmo: Drawing overlay, selected nodes: ", selected_nodes.size())

	if selected_nodes.is_empty():
		return

	# Draw gizmos only for selected nodes
	for node in selected_nodes:
		_draw_gizmos_for_node(node, overlay)

# Alternative function that might be called instead
func _forward_canvas_force_draw_over_viewport(overlay):
	print("Vector2D Gizmo: Force draw overlay called")
	_forward_canvas_draw_over_viewport(overlay)

func _draw_gizmos_for_node(node: Node, overlay):
	if not (node is Node2D):
		print("Vector2D Gizmo: Node is not Node2D: ", node.name)
		return

	var node2d = node as Node2D
	print("Vector2D Gizmo: Processing Node2D: ", node2d.name)

	# Get script properties that start with "gizmo_"
	var script = node2d.get_script()
	if script == null:
		print("Vector2D Gizmo: No script found on node: ", node2d.name)
		return

	print("Vector2D Gizmo: Script found, checking properties...")

	# Check all properties of the node
	var found_gizmo_props = false
	for prop in node2d.get_property_list():
		if prop.type == TYPE_VECTOR2 and prop.name.begins_with("gizmo_"):
			found_gizmo_props = true
			var local_pos: Vector2 = node2d.get(prop.name)
			print("Vector2D Gizmo: Found property ", prop.name, " with value: ", local_pos)

			# Convert local position to global position
			var global_pos = node2d.global_transform * local_pos
			print("Vector2D Gizmo: Global position: ", global_pos)

			# Convert global position to screen position using simpler method
			var screen_pos = global_pos  # Start simple - just use global pos as screen pos for testing
			print("Vector2D Gizmo: Drawing circle at screen position: ", screen_pos)

			# Draw the gizmo circle
			var color = Color.RED
			if dragging_info != null and dragging_info.node == node2d and dragging_info.prop == prop.name:
				color = Color.YELLOW  # Highlight when dragging

			overlay.draw_circle(screen_pos, 15.0, color)  # Make it bigger for testing
			overlay.draw_circle(screen_pos, 12.0, Color.WHITE)

	if not found_gizmo_props:
		print("Vector2D Gizmo: No gizmo_ properties found on node: ", node2d.name)

# Handle mouse for dragging gizmos (simplified for now)
func _forward_canvas_gui_input(event: InputEvent) -> bool:
	return false  # Disable input handling for now to focus on drawing

# Find a gizmo under the mouse from selected nodes (simplified for now)
func _pick_gizmo(selected_nodes: Array, mouse_pos: Vector2):
	return null  # Disable picking for now
