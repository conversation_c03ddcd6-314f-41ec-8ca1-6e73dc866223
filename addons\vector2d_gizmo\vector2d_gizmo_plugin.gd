@tool
extends EditorPlugin

# Whether gizmos are visible in the 2D editor
var show_gizmos: bool = true
# Info for current drag: { node: Node2D, prop: String }
var dragging_info = null

func _enter_tree() -> void:
	# Add "Vector2D Gizmos" submenu under View → View Options
	var menu = MenuButton.new()
	menu.text = "Vector2D Gizmos"
	var popup = menu.get_popup()
	popup.add_item("Show Gizmos", 0)
	popup.add_item("Hide Gizmos", 1)
	popup.connect("id_pressed", Callable(self, "_on_view_option_selected"))
	add_control_to_container(CONTAINER_CANVAS_EDITOR_MENU, menu)

func _exit_tree() -> void:
	# Cleanup handled by engine
	pass

func _on_view_option_selected(id: int) -> void:
	show_gizmos = (id == 0)
	update_viewport()

func update_viewport() -> void:
	# Redraw the 2D editor viewport overlays
	update_overlays()

# Draw gizmos each frame in the 2D editor
func _forward_canvas_draw_over_viewport(overlay: CanvasItem) -> void:
	if not show_gizmos:
		return
	var root = get_editor_interface().get_edited_scene_root()
	if root == null:
		return
	_draw_gizmos_recursive(root, overlay)

func _draw_gizmos_recursive(node: Node, overlay: CanvasItem) -> void:
	if node is Node2D:
		for prop in node.get_property_list():
			if prop.type == TYPE_VECTOR2 and prop.name.begins_with("gizmo_"):
				var pos: Vector2 = node.get(prop.name)
				var canvas_xform = get_editor_interface().get_canvas_transform()
				var screen_pos = canvas_xform.xform(node.global_transform.xform(pos))
				overlay.draw_circle(screen_pos, 5, Color(1, 0, 0, 0.8))
	for child in node.get_children():
		_draw_gizmos_recursive(child, overlay)

# Handle mouse for dragging gizmos
func _forward_canvas_gui_input(event: InputEvent) -> bool:
	if not show_gizmos:
		return false
	var root = get_editor_interface().get_edited_scene_root()
	if root == null:
		return false

	if event is InputEventMouseButton:
		if event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			dragging_info = _pick_gizmo(root, event.position)
			return dragging_info != null
		elif not event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			dragging_info = null
			return false
	elif event is InputEventMouseMotion and dragging_info != null:
		# Convert screen to local position
		var inv_xform = get_editor_interface().get_canvas_transform().affine_inverse()
		var local_pos = dragging_info.node.to_local(inv_xform.xform(event.position))
		dragging_info.node.set(dragging_info.prop, local_pos)
		# Refresh inspector to show updated value
		get_editor_interface().inspect_object(dragging_info.node)
		update_viewport()
		return true
	return false

# Find a gizmo under the mouse
func _pick_gizmo(node: Node, mouse_pos: Vector2):
	if node is Node2D:
		for prop in node.get_property_list():
			if prop.type == TYPE_VECTOR2 and prop.name.begins_with("gizmo_"):
				var pos: Vector2 = node.get(prop.name)
				var canvas_xform = get_editor_interface().get_canvas_transform()
				var screen_pos = canvas_xform.xform(node.global_transform.xform(pos))
				if mouse_pos.distance_to(screen_pos) <= 5:
					return {"node": node, "prop": prop.name}
	for child in node.get_children():
		var result = _pick_gizmo(child, mouse_pos)
		if result != null:
			return result
	return null
