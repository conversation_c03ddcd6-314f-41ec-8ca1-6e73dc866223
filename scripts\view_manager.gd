extends Node

@export_node_path var coffee_selection_view_path: NodePath
@export_node_path var coffee_making_view_path: NodePath

var coffee_selection_view: Control
var coffee_making_view: Control
var current_view: Control = null

func _ready():
	coffee_selection_view = get_node(coffee_selection_view_path)
	coffee_making_view = get_node(coffee_making_view_path)

	# Start with selection view
	current_view = coffee_selection_view
	#_set_visible_with_alpha(current_view, true)
	show_view_fade("selection")

func _set_active_view(view: Control):
	# Disable input for all views first
	for v in [coffee_selection_view, coffee_making_view]:
		v.mouse_filter = Control.MOUSE_FILTER_IGNORE
		v.set_process_input(false)
		v.set_process_unhandled_input(false)

	# Enable input for the active view
	view.mouse_filter = Control.MOUSE_FILTER_STOP
	view.set_process_input(true)
	view.set_process_unhandled_input(true)


func show_view_fade(target_name: String, duration := 0.4):
	var target_view: Control = null
	match target_name:
		"selection":
			target_view = coffee_selection_view
		"making":
			target_view = coffee_making_view
		_:
			push_error("Unknown view: " + target_name)
			return

	if target_view == current_view:
		return

	var fade_out_view = current_view
	var fade_in_view = target_view
	current_view = target_view
	_set_active_view(current_view)

	# Make sure the target is visible before fading it in
	fade_in_view.visible = true
	fade_in_view.modulate.a = 0.0

	var tween = create_tween()

	# Fade out old view
	if fade_out_view:
		tween.tween_property(fade_out_view, "modulate:a", 0.0, duration * 0.5)\
			.set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_OUT)
		tween.tween_callback(Callable(fade_out_view, "hide"))

	# Fade in new view
	tween.tween_interval(0.05)
	tween.tween_property(fade_in_view, "modulate:a", 1.0, duration * 0.5)\
		.set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN)

func _set_visible_with_alpha(view: Control, visible: bool):
	view.visible = visible
	view.modulate.a = 1.0 if visible else 0.0
