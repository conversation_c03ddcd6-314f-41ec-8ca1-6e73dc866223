@tool
extends Node2D

@export var minValue: float = 0.0
@export var maxValue: float = 100.0
func _get_current_value() -> float:
	return lerp(minValue,maxValue,progress)

@export var gizmo_start_position: Vector2 = Vector2(100, 100)
@export var gizmo_end_position: Vector2 = Vector2(0, 400)
	
@export var circle_point_amount: int = 16 : set = _set_circle_point_amount
@export var progress: float = 0.5 : set = _set_progress
@export var bulging_distance: float = 35.0
@export var bulging_in: bool = false
@export var rounding_circle_radius: float = 12.5 : set = _set_rounding_circle_radius

@onready var line: Line2D = $Line
@onready var glow_line: Line2D = $GlowLine
@onready var knob: Sprite2D = $Knob
@onready var label: Label = $Knob/HBoxContainer/LabelValue

var pressedDown : bool = false
var pressedUp : bool = false

func _enter_tree():
	if Engine.is_editor_hint():
		set_process(true)

func _ready():
	_update_slider()
	if not Engine.is_editor_hint():
		set_process(false)

func _process(_delta):
	if Engine.is_editor_hint():
		_update_slider()	

func _set_circle_point_amount(value):
	circle_point_amount = max(value, 2)
	_update_slider()
	
func _set_rounding_circle_radius(value):
	rounding_circle_radius = max(value, 0.0001)
	_update_slider()
	
func _set_progress(value):
	progress = min(max(value, 0.0),1.0)
	_update_slider()

func _update_slider():
	_update_line()
	_update_knob()
	_update_label()

func _update_line():
	if not is_instance_valid(line):
		return
	line.clear_points()
	
	var center_position : Vector2 = lerp(gizmo_start_position,gizmo_end_position,progress)
	var start_to_end : Vector2 = gizmo_end_position - gizmo_start_position
	var start_to_center : Vector2 = center_position - gizmo_start_position
	var end_to_center : Vector2 = center_position - gizmo_end_position
	var center_to_start : Vector2 = gizmo_start_position - center_position
	var center_to_end : Vector2 = gizmo_end_position - center_position
	var perpendicular : Vector2 = _get_perpendicular_vector(gizmo_start_position,gizmo_end_position,bulging_in)
	
	var distance_to_rounding_circle : float = sqrt(pow(bulging_distance, 2.0) + 2.0 * rounding_circle_radius * bulging_distance)
	var center_to_rounding_circle_start : Vector2 = center_position + center_to_start.normalized() * distance_to_rounding_circle - perpendicular * rounding_circle_radius
	var center_to_rounding_circle_end : Vector2 = center_position + center_to_end.normalized() * distance_to_rounding_circle - perpendicular * rounding_circle_radius
	
	var angle_start_to_end : float = Vector2.UP.angle_to(start_to_end)
	var angle_center : float = acos(distance_to_rounding_circle / (bulging_distance + rounding_circle_radius))
	var angle_rounding_circle : float = PI * 0.5 - angle_center  
	
	# line : start to circle beginning
	if start_to_end.length() * progress > bulging_distance:
		line.add_point(gizmo_start_position)
	
	# rounding circle start segment : add circle points
	if progress > 0.0:
		var rounding_circle_side = -rad_to_deg(angle_rounding_circle) if bulging_in else rad_to_deg(angle_rounding_circle)
		var rounding_circle_offset = 0.0 if bulging_in else 180.0
		var rounding_circle_points = get_circle_points(center_to_rounding_circle_start,rounding_circle_radius,rounding_circle_offset  + rad_to_deg(angle_start_to_end),rounding_circle_side,circle_point_amount/2)
		for rounding_circle_point in rounding_circle_points:
			if is_point_behind(gizmo_start_position,gizmo_end_position,rounding_circle_point):
				continue
			line.add_point(rounding_circle_point)
		
	# circle : add circle points
	var circle_angle = 180.0 - (rad_to_deg(angle_center) * 2.0) 
	var circle_side = circle_angle if bulging_in else -circle_angle
	var circle_offset = 90.0 + rad_to_deg(angle_center) if bulging_in else 90.0 - rad_to_deg(angle_center)
	var circle_points = get_circle_points(center_position,bulging_distance,circle_offset + rad_to_deg(angle_start_to_end),circle_side,circle_point_amount)
	for circle_point in circle_points:
		if is_point_behind(gizmo_start_position,gizmo_end_position,circle_point):
			continue
		line.add_point(circle_point)
		
	# rounding circle end segment : add circle points
	if progress < 1.0:
		var rounding_circle_side = -rad_to_deg(angle_rounding_circle) if bulging_in else rad_to_deg(angle_rounding_circle) 
		var rounding_circle_offset = rad_to_deg(angle_rounding_circle) if bulging_in else 180.0 -rad_to_deg(angle_rounding_circle)
		var rounding_circle_points = get_circle_points(center_to_rounding_circle_end,rounding_circle_radius,rounding_circle_offset  + rad_to_deg(angle_start_to_end),rounding_circle_side,circle_point_amount /2)
		for rounding_circle_point in rounding_circle_points:
			if is_point_behind(gizmo_start_position,gizmo_end_position,rounding_circle_point):
				continue
			line.add_point(rounding_circle_point)
		
	# line : circle beginning to end
	if start_to_end.length() * progress <  start_to_end.length() - bulging_distance:
		line.add_point(gizmo_end_position)
		
	remove_duplicate_points(line)
	glow_line.points = line.points.duplicate()

func _update_knob():
	if not is_instance_valid(knob):
		return
	var circleCenterPosition : Vector2 = lerp(gizmo_start_position, gizmo_end_position, progress)
	knob.position = circleCenterPosition

func _update_label():
	if not is_instance_valid(label):
		return
	label.text = String.num(_get_current_value(), 0) 

func _get_perpendicular_vector(start: Vector2, end: Vector2, leftHanded:bool = true) -> Vector2:
	if start.x == end.x and start.y == end.y:
		return Vector2(0.0,0.0)
		
	var dir: Vector2 = (end - start).normalized()
	if leftHanded:		
		return Vector2(-dir.y, dir.x)
	else:
		return Vector2(dir.y, -dir.x)
		
func is_point_behind(start: Vector2, end: Vector2, point: Vector2) -> bool:
	# Direction vector of the segment
	var seg = end - start
	# Project (point – start) onto the segment: 
	# t < 0 means behind `start`, t > 1 means beyond `end`
	var t = seg.dot(point - start) / seg.length_squared()
	return t < 0.0 or t > 1.0

func get_circle_points(center: Vector2, radius: float, start_angle_deg: float, sweep_angle_deg: float, point_amount: int) -> Array:
	if radius <= 0.0:
		push_error("Radius must be greater than 0.")
		return []
	
	sweep_angle_deg = clamp(sweep_angle_deg, -360.0, 360.0)
	point_amount = max(point_amount, 2)

	if sweep_angle_deg == 0.0:
		return []
	
	var points: Array = []
	
	if sweep_angle_deg == 360.0 || sweep_angle_deg == -360.0:
		# Full circle
		for i in range(point_amount):
			var angle_deg = i * (360.0 / point_amount) + start_angle_deg
			var angle_rad = deg_to_rad(angle_deg)
			points.append(center + Vector2(cos(angle_rad), sin(angle_rad)) * radius)
	else:
		# Arc
		for i in range(point_amount):
			var t = i / float(point_amount - 1)
			var angle_deg = start_angle_deg + t * sweep_angle_deg
			var angle_rad = deg_to_rad(angle_deg)
			points.append(center + Vector2(cos(angle_rad), sin(angle_rad)) * radius)

	return points
	
func remove_duplicate_points(line: Line2D) -> void:
	var unique : PackedVector2Array = PackedVector2Array()
	for p in line.points:
		if not unique.has(p):
			unique.append(p)
	line.points = unique
		
func _input(event):
	if event.is_action_pressed("ui_up"):
		pressedUp = true
	elif event.is_action_released("ui_up"):
		pressedUp = false
	elif event.is_action_pressed("ui_down"):
		pressedDown = true
	elif event.is_action_released("ui_down"):
		pressedDown = false
		
	if pressedUp:
		progress -= 0.01
	if pressedDown:
		progress += 0.01
