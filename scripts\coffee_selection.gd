extends Control

var coffee_vboxes: Array[VBoxContainer] = []
var active_tweens := {}
var selected_index := 0

# Define UI panels to show/hide (adjust node paths as needed)
@onready var coffee_selection_panel := get_node("/root/CoffeeMachine/CoffeeSelectionView")
@onready var coffee_making_panel := get_node("/root/CoffeeMachine/CoffeeMakingView")
@onready var view_manager := get_node("/root/CoffeeMachine/ViewManager") # Adjust path if needed
@onready var coffees := get_node("/root/CoffeeMachine/CoffeeSelectionView/CoffeeSelection") # Adjust path if needed


func _ready():
	setup_coffee_vboxes()
	ensure_intact_layout()
	if coffee_vboxes.size() > 0:
		_on_coffee_selected(coffee_vboxes[selected_index])

func setup_coffee_vboxes():
	for child in coffees.get_children():
		if child is Control:
			var vbox := child.get_child(0) as VBoxContainer
			coffee_vboxes.append(vbox)

func ensure_intact_layout():
	active_tweens.clear()
	for vbox in coffee_vboxes:
		var wrapper: Control = vbox.get_parent()
		wrapper.custom_minimum_size = vbox.size
		vbox.pivot_offset = vbox.size / 2

func scale_coffee(vbox: VBoxContainer, scale_factor: float, duration := 0.3):
	if active_tweens.has(vbox):
		if is_instance_valid(active_tweens[vbox]):
			active_tweens[vbox].kill()
		active_tweens.erase(vbox)

	var tween = create_tween()
	tween.tween_property(vbox, "scale", Vector2(scale_factor, scale_factor), duration)\
		.set_trans(Tween.TRANS_SINE)\
		.set_ease(Tween.EASE_OUT)

	active_tweens[vbox] = tween

func _on_coffee_selected(vbox: VBoxContainer):
	scale_coffee(vbox, 1.2, 0.3)

func _on_coffee_deselected(vbox: VBoxContainer):
	scale_coffee(vbox, 1.0, 0.1)

func _input(event):
	if event.is_action_pressed("ui_right"):
		select_next()
	elif event.is_action_pressed("ui_left"):
		select_previous()
	elif event.is_action_pressed("ui_accept"): # "Enter" key
		pick_coffee()

func select_next():
	_on_coffee_deselected(coffee_vboxes[selected_index])
	selected_index = (selected_index + 1) % coffee_vboxes.size()
	_on_coffee_selected(coffee_vboxes[selected_index])

func select_previous():
	_on_coffee_deselected(coffee_vboxes[selected_index])
	selected_index = (selected_index - 1 + coffee_vboxes.size()) % coffee_vboxes.size()
	_on_coffee_selected(coffee_vboxes[selected_index])

func pick_coffee():	
	view_manager.show_view_fade("making")
