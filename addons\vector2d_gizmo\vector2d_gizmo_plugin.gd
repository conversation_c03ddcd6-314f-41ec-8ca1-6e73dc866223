@tool
extends EditorPlugin

# Whether gizmos are visible in the 2D editor
var show_gizmos: bool = true
# Info for current drag: { node: Node2D, prop: String }
var dragging_info = null
# Reference to the menu button for cleanup
var menu_button: MenuButton = null

func _enter_tree() -> void:
	print("Vector2D Gizmo Plugin: Entering tree")
	# Add "Vector2D Gizmos" submenu under View → View Options
	menu_button = MenuButton.new()
	menu_button.text = "Vector2D Gizmos"
	var popup = menu_button.get_popup()
	popup.add_item("Show Gizmos", 0)
	popup.add_item("Hide Gizmos", 1)
	popup.connect("id_pressed", Callable(self, "_on_view_option_selected"))
	add_control_to_container(CONTAINER_CANVAS_EDITOR_MENU, menu_button)

	# Enable force draw over forwarding to ensure our overlay function gets called
	set_force_draw_over_forwarding_enabled()

func _exit_tree() -> void:
	print("Vector2D Gizmo Plugin: Exiting tree")
	# Properly cleanup the menu button
	if menu_button != null:
		remove_control_from_container(CONTAINER_CANVAS_EDITOR_MENU, menu_button)
		menu_button.queue_free()
		menu_button = null
	# Clear any dragging state
	dragging_info = null

func _on_view_option_selected(id: int) -> void:
	show_gizmos = (id == 0)
	print("Vector2D Gizmo: Show gizmos set to: ", show_gizmos)
	update_viewport()

func update_viewport() -> void:
	# Redraw the 2D editor viewport overlays
	update_overlays()

# Tell Godot which objects this plugin handles
func _handles(object) -> bool:
	if object is Node2D:
		var script = object.get_script()
		if script != null:
			# Check if the object has any gizmo_ properties
			for prop in object.get_property_list():
				if prop.type == TYPE_VECTOR2 and prop.name.begins_with("gizmo_"):
					print("Vector2D Gizmo: Handles object: ", object.name, " with property: ", prop.name)
					return true
	return false

# Draw gizmos each frame in the 2D editor
func _forward_canvas_draw_over_viewport(overlay):
	if not show_gizmos:
		return

	# Get the selected nodes from the editor selection
	var selection = get_editor_interface().get_selection()
	var selected_nodes = selection.get_selected_nodes()

	if selected_nodes.is_empty():
		return

	# Draw gizmos only for selected nodes
	for node in selected_nodes:
		_draw_gizmos_for_node(node, overlay)

# Alternative function that might be called instead
func _forward_canvas_force_draw_over_viewport(overlay):
	_forward_canvas_draw_over_viewport(overlay)

func _draw_gizmos_for_node(node: Node, overlay):
	if not (node is Node2D):
		return

	var node2d = node as Node2D

	# Get script properties that start with "gizmo_"
	var script = node2d.get_script()
	if script == null:
		return

	# Check all properties of the node
	for prop in node2d.get_property_list():
		if prop.type == TYPE_VECTOR2 and prop.name.begins_with("gizmo_"):
			var local_pos: Vector2 = node2d.get(prop.name)

			# Convert local position to global position
			var world_pos = node2d.global_transform * local_pos

			# Draw the gizmo circle at the world position
			# The overlay system automatically handles world-to-screen transformation
			var color = Color.RED
			if dragging_info != null and dragging_info.node == node2d and dragging_info.prop == prop.name:
				color = Color.YELLOW  # Highlight when dragging

			overlay.draw_circle(world_pos, 15.0, color)
			overlay.draw_circle(world_pos, 12.0, Color.WHITE)

# Handle mouse for dragging gizmos
func _forward_canvas_gui_input(event: InputEvent) -> bool:
	if not show_gizmos:
		return false

	# Get the selected nodes from the editor selection
	var selection = get_editor_interface().get_selection()
	var selected_nodes = selection.get_selected_nodes()

	if selected_nodes.is_empty():
		return false

	if event is InputEventMouseButton:
		if event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			dragging_info = _pick_gizmo(selected_nodes, event.position)
			if dragging_info != null:
				print("Vector2D Gizmo: Started dragging ", dragging_info.prop)
			return dragging_info != null
		elif not event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			if dragging_info != null:
				print("Vector2D Gizmo: Stopped dragging ", dragging_info.prop)
				dragging_info = null
				return true
			return false
	elif event is InputEventMouseMotion and dragging_info != null:
		# Convert screen position to world position
		var canvas_transform = get_editor_interface().get_editor_viewport_2d().get_canvas_transform()
		var world_pos = canvas_transform.affine_inverse() * event.position

		# Convert world position to local position relative to the node
		var local_pos = dragging_info.node.to_local(world_pos)

		# Update the property
		dragging_info.node.set(dragging_info.prop, local_pos)

		# The inspector will automatically update when the property changes
		update_viewport()
		return true
	return false

# Find a gizmo under the mouse from selected nodes
func _pick_gizmo(selected_nodes: Array, mouse_pos: Vector2):
	for node in selected_nodes:
		if not (node is Node2D):
			continue

		var node2d = node as Node2D
		var script = node2d.get_script()
		if script == null:
			continue

		# Check all Vector2 properties that start with "gizmo_"
		for prop in node2d.get_property_list():
			if prop.type == TYPE_VECTOR2 and prop.name.begins_with("gizmo_"):
				var local_pos: Vector2 = node2d.get(prop.name)

				# Convert local position to world position
				var world_pos = node2d.global_transform * local_pos

				# Convert world position to screen position for mouse comparison
				var canvas_transform = get_editor_interface().get_editor_viewport_2d().get_canvas_transform()
				var screen_pos = canvas_transform * world_pos

				# Check if mouse is within gizmo radius
				if mouse_pos.distance_to(screen_pos) <= 15.0:  # Use larger radius to match drawing
					return {"node": node2d, "prop": prop.name}

	return null
